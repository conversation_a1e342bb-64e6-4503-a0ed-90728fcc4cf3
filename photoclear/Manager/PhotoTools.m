//
//  PhotoTools.m
//  photoclear
//
//  Created by lifubing on 2021/6/29.
//

#import "PhotoTools.h"
#import "Preferences.h"

@implementation PhotoTools

+ (PHFetchResult<PHAsset *> *)allPhotos {
    PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
    fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
    //容器类
    return [PHAsset fetchAssetsWithMediaType:PHAssetMediaTypeImage options:fetchOptions]; //得到所有图片
    /*
     PHAssetMediaType：
     PHAssetMediaTypeUnknown = 0,//在这个配置下，请求不会返回任何东西
     PHAssetMediaTypeImage   = 1,//图片
     PHAssetMediaTypeVideo   = 2,//视频
     PHAssetMediaTypeAudio   = 3,//音频
     */
//    [self.containView.collectionView reloadData];
}

+ (NSURL *)sharedContainerURL {
    return [[NSFileManager defaultManager] containerURLForSecurityApplicationGroupIdentifier:@"group.com.lfb.manager.photoclear.shared"];
}

static BOOL isIncacheRandom = NO;

+ (void)asyncCacheRandomPhotosForWidgetCompletion:(void(^)(BOOL success))completion {
    NSInteger count = 100;
    
    if (isIncacheRandom) {
        return;
    }
    isIncacheRandom = YES;
    // 检查相册权限
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
    if (status != PHAuthorizationStatusAuthorized) {
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            if (status == PHAuthorizationStatusAuthorized) {
                
                dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                    [self performPhotoCaching:count completion:^(BOOL success) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            isIncacheRandom = NO;
                            completion(success);
                        });
                    }];
                });
                
            } else {
                isIncacheRandom = NO;
                completion(NO);
            }
        }];
    } else {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            [self performPhotoCaching:count completion:^(BOOL success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    isIncacheRandom = NO;
                    completion(success);
                });
            }];
        });
    }
}

+ (void)normalCacheSmallRandomPhotosForWidgetCompletion:(void(^)(BOOL success))completion {
    NSInteger count = 100;
    // 检查相册权限
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
    if (status != PHAuthorizationStatusAuthorized) {
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            if (status == PHAuthorizationStatusAuthorized) {
                [self performPhotoCaching:count completion:completion];
            } else {
                completion(NO);
            }
        }];
    } else {
        [self performPhotoCaching:count completion:completion];
    }
}

+ (void)performPhotoCaching:(NSInteger)count completion:(void(^)(BOOL success))completion {
    
    PHFetchResult<PHAsset *> *allPhotos = [self allPhotos];
    
    if (allPhotos.count == 0) {
        completion(NO);
        return;
    }
    
    // 获取共享容器目录
    NSURL *sharedURL = [self sharedContainerURL];
    if (!sharedURL) {
        completion(NO);
        return;
    }
    
    NSURL *photosDir = [sharedURL URLByAppendingPathComponent:@"WidgetPhotos"];
    
    // 创建目录
    NSError *error;
    [[NSFileManager defaultManager] createDirectoryAtURL:photosDir 
                             withIntermediateDirectories:YES 
                                              attributes:nil 
                                                   error:&error];
    
    // 清理旧照片
    [self cleanOldCachedPhotos];
    
    // 获取已操作过的照片列表和缓存次数记录
    NSArray *operatedPhotos = [self getOperatedPhotos];
    NSDictionary *photoCacheCount = [self getPhotoCacheCount];
    
    // 智能选择照片，优先选择未缓存或缓存次数较少的照片
    NSMutableArray *randomIndexes = [NSMutableArray array];
    NSInteger totalPhotos = allPhotos.count;
    NSInteger photosToCache = MIN(count, totalPhotos);
    
    // 创建照片优先级数组 (index, priority)
    NSMutableArray *photoPriorities = [NSMutableArray array];
    
    for (NSInteger i = 0; i < totalPhotos; i++) {
        PHAsset *asset = [allPhotos objectAtIndex:i];
        NSString *photoKey = [asset.localIdentifier componentsSeparatedByString:@"/"].firstObject;
        
        NSInteger priority = 0;
        
        // 如果照片已被操作过，降低优先级
        if ([operatedPhotos containsObject:photoKey]) {
            priority += 1000; // 已操作过的照片优先级较低
        }
        
        // 根据缓存次数设置优先级，缓存次数越多优先级越低
        NSNumber *cacheCount = photoCacheCount[photoKey];
        if (cacheCount) {
            priority += cacheCount.integerValue * 10; // 每缓存一次增加100优先级值
        }
        
        // 添加随机因子，避免完全按顺序选择
        priority += arc4random_uniform(50);
        
        [photoPriorities addObject:@{
            @"index": @(i),
            @"priority": @(priority),
            @"photoKey": photoKey,
            @"cacheCount": cacheCount ?: @(0)
        }];
    }
    
    // 按优先级排序（优先级值越小越优先）
    [photoPriorities sortUsingComparator:^NSComparisonResult(NSDictionary *obj1, NSDictionary *obj2) {
        NSNumber *priority1 = obj1[@"priority"];
        NSNumber *priority2 = obj2[@"priority"];
        return [priority1 compare:priority2];
    }];
    
    // 选择前photosToCache张照片
    for (NSInteger i = 0; i < photosToCache && i < photoPriorities.count; i++) {
        NSDictionary *photoInfo = photoPriorities[i];
        [randomIndexes addObject:photoInfo[@"index"]];
        
//        NSLog(@"选择照片 index:%@ 缓存次数:%@ 优先级:%@", photoInfo[@"index"], photoInfo[@"cacheCount"], photoInfo[@"priority"]);
    }
    
    __block NSInteger completedCount = 0;
    __block BOOL hasError = NO;
    
    PHImageManager *imageManager = [PHImageManager defaultManager];
    PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
    options.synchronous = NO;
    options.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    options.resizeMode = PHImageRequestOptionsResizeModeExact;
    
    for (NSNumber *indexNumber in randomIndexes) {
        @autoreleasepool {
            PHAsset *asset = [allPhotos objectAtIndex:indexNumber.integerValue];
            
            [imageManager requestImageForAsset:asset
                                    targetSize:CGSizeMake(1000, 1000)
                                   contentMode:PHImageContentModeAspectFit
                                       options:options
                                 resultHandler:^(UIImage *result, NSDictionary *info) {
                if (result) {
                    NSString *fileName = [NSString stringWithFormat:@"photo_%@.jpg", asset.localIdentifier];
                    // 清理文件名中的特殊字符
                    fileName = [[fileName componentsSeparatedByCharactersInSet:[[NSCharacterSet alphanumericCharacterSet] invertedSet]] componentsJoinedByString:@"_"];
                    fileName = [fileName stringByAppendingString:@".jpg"];
                    
                    NSURL *fileURL = [photosDir URLByAppendingPathComponent:fileName];
                    
                    NSData *imageData = UIImageJPEGRepresentation(result, 0.8);
                    
                    // 检查图片大小，如果大于30MB则进一步压缩
                    const NSUInteger maxSize = 20 * 1024 * 1024; // 30MB
                    if (imageData.length > maxSize) {
                        CGFloat compressionQuality = 0.8;
                        while (imageData.length > maxSize && compressionQuality > 0.1) {
                            compressionQuality -= 0.1;
                            imageData = UIImageJPEGRepresentation(result, compressionQuality);
                        }
                        NSLog(@"图片过大，已压缩至质量: %.1f, 大小: %.2fMB", compressionQuality, imageData.length / (1024.0 * 1024.0));
                    }
                    
                    if ([imageData writeToURL:fileURL atomically:YES]) {
                        // 创建照片元数据
                        NSMutableDictionary *metadata = [NSMutableDictionary dictionary];
                        metadata[@"localIdentifier"] = asset.localIdentifier;
                        
                        // 添加创建时间
                        if (asset.creationDate) {
                            metadata[@"creationDate"] = @([asset.creationDate timeIntervalSince1970]);
                        }
                        
                        // 添加修改时间
                        if (asset.modificationDate) {
                            metadata[@"modificationDate"] = @([asset.modificationDate timeIntervalSince1970]);
                        }
                        
                        // 保存元数据到JSON文件
                        NSString *metadataFileName = [fileName stringByReplacingOccurrencesOfString:@".jpg" withString:@"_metadata.json"];
                        NSURL *metadataURL = [photosDir URLByAppendingPathComponent:metadataFileName];
                        
                        NSError *jsonError;
                        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:metadata options:NSJSONWritingPrettyPrinted error:&jsonError];
                        if (jsonData && !jsonError) {
                            [jsonData writeToURL:metadataURL atomically:YES];
                        }
                        
                        // 更新照片缓存次数记录
                        [self incrementCacheCountForPhoto:asset.localIdentifier];
                        
    //                    NSLog(@"成功缓存照片: %@ size:%ld", fileName, imageData.length);
                    } else {
                        hasError = YES;
                        NSLog(@"缓存照片失败: %@", fileName);
                    }
                } else {
                    hasError = YES;
                    NSLog(@"获取照片失败");
                }
                
                completedCount++;
                if (completedCount == photosToCache) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        completion(!hasError);
                    });
                }
            }];
        }
    }
}

+ (void)cleanOldCachedPhotos {
    NSURL *sharedURL = [self sharedContainerURL];
    if (!sharedURL) return;
    
    NSURL *photosDir = [sharedURL URLByAppendingPathComponent:@"WidgetPhotos"];
    
    NSError *error;
    NSArray *files = [[NSFileManager defaultManager] contentsOfDirectoryAtURL:photosDir
                                                   includingPropertiesForKeys:nil
                                                                      options:0
                                                                        error:&error];
    
    for (NSURL *fileURL in files) {
        [[NSFileManager defaultManager] removeItemAtURL:fileURL error:nil];
    }
}

+ (NSArray<NSString *> *)getCachedPhotoNames {
    NSURL *sharedURL = [self sharedContainerURL];
    if (!sharedURL) return @[];
    
    NSURL *photosDir = [sharedURL URLByAppendingPathComponent:@"WidgetPhotos"];
    
    NSError *error;
    NSArray *files = [[NSFileManager defaultManager] contentsOfDirectoryAtURL:photosDir
                                                   includingPropertiesForKeys:nil
                                                                      options:0
                                                                        error:&error];
    
    NSMutableArray *photoNames = [NSMutableArray array];
    for (NSURL *fileURL in files) {
        if ([fileURL.pathExtension.lowercaseString isEqualToString:@"jpg"]) {
            [photoNames addObject:fileURL.lastPathComponent];
        }
    }
    
    return [photoNames copy];
}

+ (NSArray<NSString *> *)getOperatedPhotos {
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    return [sharedDefaults arrayForKey:@"operatedPhotos"] ?: @[];
}

+ (void)clearOperatedPhotos {
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    [sharedDefaults removeObjectForKey:@"operatedPhotos"];
    [sharedDefaults synchronize];
    NSLog(@"已清空操作过的照片记录");
}

#pragma mark - 照片缓存次数管理

+ (NSDictionary<NSString *, NSNumber *> *)getPhotoCacheCount {
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    return [sharedDefaults dictionaryForKey:@"photoCacheCount"] ?: @{};
}

+ (void)incrementCacheCountForPhoto:(NSString *)localIdentifier {
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    NSMutableDictionary *cacheCount = [[self getPhotoCacheCount] mutableCopy];
    
    NSString *photoKey = [localIdentifier componentsSeparatedByString:@"/"].firstObject;
    NSNumber *currentCount = cacheCount[photoKey] ?: @(0);
    cacheCount[photoKey] = @(currentCount.integerValue + 1);
    
    [sharedDefaults setObject:cacheCount forKey:@"photoCacheCount"];
    [sharedDefaults synchronize];
}

+ (void)clearPhotoCacheCount {
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    [sharedDefaults removeObjectForKey:@"photoCacheCount"];
    [sharedDefaults synchronize];
    NSLog(@"已清空照片缓存次数记录");
}

@end
